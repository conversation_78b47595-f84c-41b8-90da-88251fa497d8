"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import EconomicCalendar from "@/components/economic-calendar"
import Footer from "@/components/footer"

export default function EconomicCalendarPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Navigation */}
      <nav className="p-8">
        <Link href="/">
          <Button
            variant="ghost"
            className="text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </Link>
      </nav>

      {/* Page Content */}
      <EconomicCalendar />
      <Footer />
    </main>
  )
}
