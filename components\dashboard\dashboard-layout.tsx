"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  TrendingUp,
  BarChart3,
  Plus,
  Wallet,
  Users,
  Award,
  FileText,
  CreditCard,
  Gift,
  BookOpen,
  Ticket,
  HelpCircle,
  MessageSquare,
  LogOut,
  Settings,
  Bell,
  Search,
  Menu,
  X,
  ChevronRight,
  Crown,
  Star,
  Zap,
  Shield,
  ChevronDown,
  Globe,
  Smartphone,
  Sun,
  Moon,
  ArrowLeft,
  FolderOpen,
  Share2,
  User,
  Diamond,
} from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import LanguageSelector from "@/components/language-selector"
import { useLanguage } from "@/contexts/language-context"
import Image from "next/image"

interface DashboardLayoutProps {
  children: React.ReactNode
  activeTab: string
  onTabChange: (tab: string) => void
}

export default function DashboardLayout({ children, activeTab, onTabChange }: DashboardLayoutProps) {
  const [isSidebarO<PERSON>, setIsSidebarOpen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t, isRTL } = useLanguage()
  const pathname = usePathname()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Check localStorage first, then system preference
      const savedTheme = localStorage.getItem("theme")
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      const shouldUseDark = savedTheme ? savedTheme === "dark" : prefersDark
      
      setIsDarkMode(shouldUseDark)
      // Apply initial theme
      if (shouldUseDark) {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    }
  }, [])

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode
    setIsDarkMode(newDarkMode)
    document.documentElement.classList.toggle("dark")
    // Save theme preference to localStorage
    localStorage.setItem("theme", newDarkMode ? "dark" : "light")
  }

  const menuItems = [
    { 
      id: "overview", 
      label: t("dashboard.overview"), 
      icon: BarChart3,
      badge: null,
      description: "Account overview and analytics"
    },
    { 
      id: "new-challenge", 
      label: t("dashboard.newChallenge"), 
      icon: Plus,
      badge: null,
      description: "Start a new trading challenge"
    },
    { 
      id: "withdraw", 
      label: t("dashboard.withdraw"), 
      icon: Wallet,
      badge: null,
      description: "Withdraw your profits"
    },
    { 
      id: "referral", 
      label: t("dashboard.referral"), 
      icon: Users,
      badge: null,
      description: "Invite friends and earn bonuses"
    },
    { 
      id: "affiliate", 
      label: t("dashboard.affiliate"), 
      icon: Award,
      badge: null,
      description: "Partner program dashboard"
    },
    { 
      id: "certificate", 
      label: t("dashboard.certificate"), 
      icon: FileText,
      badge: null,
      description: "View your trading certificates"
    },
    { 
      id: "transactions", 
      label: t("dashboard.transactions"), 
      icon: CreditCard,
      badge: null,
      description: "Transaction history"
    },
    { 
      id: "rewards", 
      label: t("dashboard.rewards"), 
      icon: Gift,
      badge: null,
      description: "Claim your rewards"
    },
    { 
      id: "trading-rules", 
      label: t("dashboard.tradingRules"), 
      icon: BookOpen,
      badge: null,
      description: "Trading rules and guidelines"
    },
    { 
      id: "ticket", 
      label: t("dashboard.ticket"), 
      icon: Ticket,
      badge: "2",
      description: "Support tickets"
    },
    { 
      id: "faqs", 
      label: t("dashboard.faqs"), 
      icon: HelpCircle,
      badge: null,
      description: "Frequently asked questions"
    },
    {
      id: "support",
      label: t("dashboard.support"),
      icon: MessageSquare,
      badge: null,
      description: "Get help and support"
    },
    {
      id: "resources",
      label: "Resources",
      icon: FolderOpen,
      badge: null,
      description: "Trading tools and market information"
    },
    {
      id: "profile",
      label: "Profile",
      icon: User,
      badge: null,
      description: "Your profile and points"
    },
    {
      id: "social-media",
      label: "Social Media",
      icon: Share2,
      badge: null,
      description: "Connect with our community"
    },
  ]

  const toolsServices = [
    { label: "Trading Tools", icon: BarChart3 },
    { label: "Analytics", icon: TrendingUp },
    { label: "Reports", icon: FileText },
  ]

  const supportItems = [
    { label: "Help Center", icon: HelpCircle },
    { label: "Contact Support", icon: MessageSquare },
    { label: "Documentation", icon: BookOpen },
  ]

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Custom CSS for subtle scrollbar without arrows */}
      <style jsx>{`
        .sidebar-scroll::-webkit-scrollbar {
          width: 4px;
        }
        .sidebar-scroll::-webkit-scrollbar-track {
          background: transparent;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb {
          background: rgba(156, 163, 175, 0.3);
          border-radius: 2px;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.5);
        }
        .sidebar-scroll::-webkit-scrollbar-button {
          display: none;
        }
        .sidebar-scroll {
          scrollbar-width: thin;
          scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
        }
      `}</style>

      {/* Top Header Bar - Fixed at top */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gray-800 dark:bg-gray-900 text-white px-6 py-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-lg overflow-hidden">
              <Image
                src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
                alt="Forex Throne Logo"
                width={24}
                height={24}
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-xl font-bold">FxThrone</span>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 cursor-pointer">
            <span className="text-white">Badar</span>
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">BA</span>
            </div>
            <Bell className="w-5 h-5 text-white" />
          </div>
        </div>
      </div>

      {/* Main Content Area - Below header */}
      <div className="flex pt-16"> {/* pt-16 adds space below fixed header */}
        {/* Left Sidebar - Fixed/Sticky with internal scroll */}
        <div className="fixed left-4 top-20 w-64 bg-white dark:bg-gray-800 h-[calc(100vh-5rem)] border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden">
          <div className="h-full overflow-y-auto p-6 sidebar-scroll">
            {/* New FxThrone Challenge Button */}
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white mb-8 py-3">
              <Plus className="w-4 h-4 mr-2" />
              New FxThrone Challenge
            </Button>

            {/* Navigation Menu */}
            <nav className="space-y-2">
              {menuItems.slice(0, 9).map((item) => {
                const Icon = item.icon
                const currentPath = pathname?.split('/').pop() || 'overview'
                const isActive = currentPath === item.id || (currentPath === 'overview' && item.id === 'overview')
                
                return (
                  <Link
                    key={item.id}
                    href={`/dashboard/${item.id === 'overview' ? 'overview' : item.id}`}
                    onClick={() => {
                      onTabChange(item.id)
                      setIsSidebarOpen(false)
                    }}
                    className={`flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? "bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="flex-1 text-left">{item.label}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="text-xs bg-red-500 hover:bg-red-600">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                )
              })}
            </nav>

            {/* Tools & Services Section */}
            <div className="mt-8">
              <button className="w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                <div className="flex items-center gap-3">
                  <Shield className="w-4 h-4" />
                  <span>Tools & Services</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>

            {/* Support Section */}
            <div className="mt-2">
              <button className="w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                <div className="flex items-center gap-3">
                  <HelpCircle className="w-4 h-4" />
                  <span>Support</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>

            {/* Mobile App Section */}
            <div className="mt-2">
              <button className="w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                <div className="flex items-center gap-3">
                  <Smartphone className="w-4 h-4" />
                  <span>Mobile app</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>

            {/* Settings & Preferences */}
            <div className="mt-8 space-y-2">
              <button 
                onClick={toggleTheme}
                className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              >
                <div className="w-4 h-4 rounded-full bg-yellow-400"></div>
                <span>{isDarkMode ? "Light mode" : "Dark mode"}</span>
              </button>
              
              <Link href="/">
                <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                  <div className="w-4 h-4 bg-black rounded"></div>
                  <span>Back to website</span>
                  <ChevronRight className="w-4 h-4 ml-auto" />
                </button>
              </Link>

              <div className="flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300">
                <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">🇬🇧</span>
                </div>
                <span>English</span>
                <ChevronDown className="w-4 h-4 ml-auto" />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area - With proper spacing and margin for fixed sidebar */}
        <div className="flex-1 bg-gray-100 dark:bg-gray-900 min-h-screen ml-72 mr-4 mt-4">
          <div className="p-8">
            {/* Breadcrumb */}
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Trader / Accounts overview
            </div>
            
            {/* Page Content */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Chat Bubble */}
      <div className="fixed bottom-6 right-6">
        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-blue-600 transition-colors">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-white rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>

      {/* Starburst Icon */}
      <div className="fixed top-4 right-4">
        <div className="w-8 h-8 bg-yellow-400 rounded-lg flex items-center justify-center shadow-lg">
          <Star className="w-4 h-4 text-yellow-800" />
        </div>
      </div>
    </div>
  )
}
