"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { 
  ExternalLink,
  Users,
  MessageCircle,
  Share2,
  Heart,
  Eye,
  TrendingUp,
  Bell,
  Star,
  Play
} from "lucide-react"
import { useState } from "react"

export default function SocialMedia() {
  const [followedPlatforms, setFollowedPlatforms] = useState<string[]>(['twitter', 'youtube', 'telegram'])

  const socialPlatforms = [
    {
      id: 'facebook',
      name: 'Facebook',
      handle: '@ApexCapitalTrading',
      followers: '125K',
      description: 'Daily trading insights, market analysis, and community discussions',
      url: 'https://facebook.com/apexcapitaltrading',
      color: 'from-blue-600 to-blue-700',
      logo: '📘',
      stats: {
        posts: 245,
        engagement: '8.5%',
        lastPost: '2 hours ago'
      }
    },
    {
      id: 'twitter',
      name: 'Twitter/X',
      handle: '@ApexCapital',
      followers: '89K',
      description: 'Real-time market updates, trading signals, and breaking news',
      url: 'https://twitter.com/apexcapital',
      color: 'from-black to-gray-800',
      logo: '𝕏',
      stats: {
        posts: 1240,
        engagement: '12.3%',
        lastPost: '15 minutes ago'
      }
    },
    {
      id: 'instagram',
      name: 'Instagram',
      handle: '@apexcapital_trading',
      followers: '67K',
      description: 'Visual trading education, success stories, and behind-the-scenes content',
      url: 'https://instagram.com/apexcapital_trading',
      color: 'from-pink-500 via-red-500 to-yellow-500',
      logo: '📷',
      stats: {
        posts: 156,
        engagement: '15.7%',
        lastPost: '1 hour ago'
      }
    },
    {
      id: 'youtube',
      name: 'YouTube',
      handle: 'Apex Capital Trading',
      followers: '234K',
      description: 'Comprehensive trading tutorials, market analysis, and live sessions',
      url: 'https://youtube.com/@apexcapitaltrading',
      color: 'from-red-600 to-red-700',
      logo: '📺',
      stats: {
        posts: 89,
        engagement: '18.2%',
        lastPost: '3 hours ago'
      }
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      handle: 'Apex Capital',
      followers: '45K',
      description: 'Professional trading insights, industry news, and career opportunities',
      url: 'https://linkedin.com/company/apexcapital',
      color: 'from-blue-700 to-blue-800',
      logo: '💼',
      stats: {
        posts: 78,
        engagement: '9.8%',
        lastPost: '4 hours ago'
      }
    },
    {
      id: 'telegram',
      name: 'Telegram',
      handle: '@ApexCapitalSignals',
      followers: '156K',
      description: 'Exclusive trading signals, market alerts, and community chat',
      url: 'https://t.me/apexcapitalsignals',
      color: 'from-blue-500 to-cyan-500',
      logo: '✈️',
      stats: {
        posts: 567,
        engagement: '25.4%',
        lastPost: '5 minutes ago'
      }
    },
    {
      id: 'discord',
      name: 'Discord',
      handle: 'Apex Capital Community',
      followers: '78K',
      description: 'Live trading discussions, voice chats, and community support',
      url: 'https://discord.gg/apexcapital',
      color: 'from-indigo-600 to-purple-600',
      logo: '🎮',
      stats: {
        posts: 2340,
        engagement: '32.1%',
        lastPost: '1 minute ago'
      }
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      handle: '@apexcapitalfx',
      followers: '123K',
      description: 'Quick trading tips, market reactions, and educational content',
      url: 'https://tiktok.com/@apexcapitalfx',
      color: 'from-black via-red-500 to-cyan-400',
      logo: '🎵',
      stats: {
        posts: 234,
        engagement: '28.9%',
        lastPost: '30 minutes ago'
      }
    }
  ]

  const communityStats = {
    totalFollowers: '917K',
    totalEngagement: '18.7%',
    monthlyGrowth: '+12.5%',
    activeCommunities: 8
  }

  const recentPosts = [
    {
      platform: 'Twitter/X',
      content: 'EURUSD breaking key resistance at 1.0850! Watch for potential continuation to 1.0900 📈 #ForexTrading',
      engagement: '2.3K likes, 456 retweets',
      time: '15 minutes ago'
    },
    {
      platform: 'YouTube',
      content: 'New Video: "How to Trade NFP Like a Pro" - Complete guide with live examples',
      engagement: '15K views, 1.2K likes',
      time: '3 hours ago'
    },
    {
      platform: 'Telegram',
      content: '🚨 SIGNAL ALERT: GBPUSD LONG at 1.2720, TP: 1.2780, SL: 1.2680',
      engagement: '567 reactions',
      time: '5 minutes ago'
    }
  ]

  const toggleFollow = (platformId: string) => {
    setFollowedPlatforms(prev => 
      prev.includes(platformId) 
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Social Media Hub 🌐</h1>
            <p className="text-lg text-gray-700 dark:text-white/70">Connect with our trading community across all platforms</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-purple-600 dark:bg-purple-500 text-white">
              <Users className="w-4 h-4 mr-1" />
              {communityStats.totalFollowers} Followers
            </Badge>
          </div>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-2xl p-4">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <Users className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{communityStats.totalFollowers}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Total Followers</div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-2xl p-4">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{communityStats.totalEngagement}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Avg Engagement</div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-2xl p-4">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{communityStats.monthlyGrowth}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Monthly Growth</div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 backdrop-blur-sm border border-orange-200 dark:border-orange-400/20 rounded-2xl p-4">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center shadow-lg">
                <Share2 className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{communityStats.activeCommunities}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Active Platforms</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Social Media Platforms */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {socialPlatforms.map((platform) => {
          const isFollowed = followedPlatforms.includes(platform.id)
          return (
            <div key={platform.id} className={`bg-gradient-to-br ${platform.color} backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="text-3xl">{platform.logo}</div>
                  <div>
                    <h3 className="text-lg font-bold text-white">{platform.name}</h3>
                    <p className="text-white/80 text-sm">{platform.handle}</p>
                  </div>
                </div>
                <Badge className="bg-white/20 text-white border-white/30">
                  {platform.followers}
                </Badge>
              </div>

              <p className="text-white/90 text-sm mb-4 line-clamp-2">{platform.description}</p>

              <div className="grid grid-cols-3 gap-2 mb-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-white">{platform.stats.posts}</div>
                  <div className="text-xs text-white/70">Posts</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-white">{platform.stats.engagement}</div>
                  <div className="text-xs text-white/70">Engagement</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-white/70">Last Post</div>
                  <div className="text-xs font-medium text-white">{platform.stats.lastPost}</div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  className="flex-1 bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm"
                  onClick={() => window.open(platform.url, '_blank')}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Visit
                </Button>
                <Button
                  variant={isFollowed ? "secondary" : "outline"}
                  size="sm"
                  onClick={() => toggleFollow(platform.id)}
                  className={`${isFollowed ? 'bg-white text-gray-900' : 'bg-white/20 text-white border-white/30'} hover:bg-white hover:text-gray-900`}
                >
                  {isFollowed ? <Bell className="w-4 h-4" /> : <Star className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          )
        })}
      </div>

      {/* Recent Activity */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">Recent Activity</h3>
          <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
            View All Activity
          </Button>
        </div>

        <div className="space-y-4">
          {recentPosts.map((post, index) => (
            <div key={index} className="flex items-start gap-4 p-4 rounded-xl bg-gray-50 dark:bg-white/5 hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center shadow-lg">
                <MessageCircle className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <Badge variant="secondary" className="text-xs">{post.platform}</Badge>
                  <span className="text-xs text-gray-500 dark:text-white/50">{post.time}</span>
                </div>
                <p className="text-gray-900 dark:text-white mb-2">{post.content}</p>
                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-white/60">
                  <span className="flex items-center gap-1">
                    <Heart className="w-4 h-4" />
                    {post.engagement}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Community Guidelines */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Community Guidelines</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-semibold text-gray-900 dark:text-white">✅ Do's</h4>
            <ul className="text-sm text-gray-700 dark:text-white/80 space-y-1">
              <li>• Share constructive trading insights</li>
              <li>• Ask questions and help others learn</li>
              <li>• Follow platform-specific rules</li>
              <li>• Engage respectfully with the community</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold text-gray-900 dark:text-white">❌ Don'ts</h4>
            <ul className="text-sm text-gray-700 dark:text-white/80 space-y-1">
              <li>• Share unverified trading advice</li>
              <li>• Spam or self-promote excessively</li>
              <li>• Share personal account details</li>
              <li>• Engage in toxic behavior</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
