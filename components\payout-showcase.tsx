"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, Star, TrendingUp, DollarSign, Award, Users, Zap } from "lucide-react"
import Image from "next/image"
import { useLanguage } from "@/contexts/language-context"

const certificates = [
  {
    id: 1,
    name: "<PERSON>",
    amount: "$12,450",
    date: "2024-01-15",
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116514/<PERSON>_<PERSON>_<PERSON>_FxThrone_Payout_Certificate_fe4mkh.jpg",
    status: "Paid",
    challenge: "Phase 2",
    profit: "+24.5%"
  },
  {
    id: 2,
    name: "<PERSON>",
    amount: "$8,750",
    date: "2024-01-12",
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116498/<PERSON>_<PERSON>_FxThrone_Payout_Certificate_ibwoit.jpg",
    status: "Paid",
    challenge: "Phase 1",
    profit: "+17.5%"
  },
  {
    id: 3,
    name: "<PERSON> <PERSON>wangi",
    amount: "$15,200",
    date: "2024-01-10",
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116484/Kevin_Mwangi_FxThrone_Payout_Certificate_j2wsb7.jpg",
    status: "Paid",
    challenge: "Phase 2",
    profit: "+30.4%"
  },
  {
    id: 4,
    name: "Hassan Farah",
    amount: "$9,800",
    date: "2024-01-08",
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116482/Hassan_Farah_FxThrone_Payout_Certificate_l7mojs.jpg",
    status: "Paid",
    challenge: "Phase 1",
    profit: "+19.6%"
  },
  {
    id: 5,
    name: "Imran Tariq",
    amount: "$11,300",
    date: "2024-01-05",
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116459/Imran_Tariq_FxThrone_Payout_Certificate_tiv0bj.jpg",
    status: "Paid",
    challenge: "Phase 2",
    profit: "+22.6%"
  }
]

export default function PayoutShowcase() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const { t } = useLanguage()

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % certificates.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, certificates.length])

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % certificates.length)
    setIsAutoPlaying(false)
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + certificates.length) % certificates.length)
    setIsAutoPlaying(false)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50 dark:from-slate-900 dark:via-gray-900 dark:to-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-12 h-12 rounded-xl overflow-hidden shadow-lg">
              <Image
                src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
                alt="Forex Throne Logo"
                width={48}
                height={48}
                className="w-full h-full object-cover"
              />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
              Real Payout Certificates
            </h2>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            See the actual payout certificates from our successful traders. These are real profits earned through our prop trading challenges.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            <DollarSign className="w-8 h-8 text-green-500 mx-auto mb-3" />
            <div className="text-2xl font-bold text-gray-900 dark:text-white">$57.5M+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Paid Out</div>
          </div>
          <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            <Users className="w-8 h-8 text-blue-500 mx-auto mb-3" />
            <div className="text-2xl font-bold text-gray-900 dark:text-white">15,000+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Successful Traders</div>
          </div>
          <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            <Award className="w-8 h-8 text-yellow-500 mx-auto mb-3" />
            <div className="text-2xl font-bold text-gray-900 dark:text-white">98.5%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Success Rate</div>
          </div>
          <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            <Zap className="w-8 h-8 text-purple-500 mx-auto mb-3" />
            <div className="text-2xl font-bold text-gray-900 dark:text-white">24/7</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Support Available</div>
          </div>
        </div>

        {/* Certificate Carousel */}
        <div className="relative">
          {/* Navigation Buttons */}
          <Button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
            size="icon"
          >
            <ChevronLeft className="w-6 h-6" />
          </Button>
          
          <Button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
            size="icon"
          >
            <ChevronRight className="w-6 h-6" />
          </Button>

          {/* Certificate Display */}
          <div className="max-w-4xl mx-auto">
            <Card className="overflow-hidden shadow-2xl border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
              <CardContent className="p-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                  {/* Certificate Image */}
                  <div className="relative h-96 lg:h-full min-h-[400px] bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-gray-800 dark:to-gray-900">
                    <Image
                      src={certificates[currentIndex].image}
                      alt={`${certificates[currentIndex].name} Payout Certificate`}
                      fill
                      className="object-cover"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                    <div className="absolute bottom-4 left-4 right-4">
                      <Badge className="bg-green-500 hover:bg-green-600 text-white border-0">
                        <Star className="w-3 h-3 mr-1" />
                        Verified Payout
                      </Badge>
                    </div>
                  </div>

                  {/* Certificate Details */}
                  <div className="p-8 lg:p-12 flex flex-col justify-center">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        {certificates[currentIndex].name}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Professional Trader • {certificates[currentIndex].challenge}
                      </p>
                    </div>

                    <div className="space-y-6">
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Payout Amount</p>
                          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                            {certificates[currentIndex].amount}
                          </p>
                        </div>
                        <TrendingUp className="w-8 h-8 text-green-500" />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                          <p className="text-sm text-gray-600 dark:text-gray-400">Profit</p>
                          <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                            {certificates[currentIndex].profit}
                          </p>
                        </div>
                        <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
                          <p className="text-sm text-gray-600 dark:text-gray-400">Date</p>
                          <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                            {new Date(certificates[currentIndex].date).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Certificate Status</p>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                          <span className="font-semibold text-green-600 dark:text-green-400">
                            {certificates[currentIndex].status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 gap-2">
            {certificates.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-blue-600 dark:bg-blue-400 scale-125"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Join Our Success Stories?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Start your trading journey today and become the next success story. Our proven system has helped thousands of traders achieve their financial goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-8 py-3 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
              Start Trading Challenge
            </Button>
            <Button variant="outline" className="border-2 border-gray-300 dark:border-gray-600 px-8 py-3 text-lg rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-300">
              View All Certificates
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
} 